services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz
    restart: always
    environment:
      # 域名配置 - 使用你的域名 postiz.eric-n8n.online
      MAIN_URL: "https://postiz.eric-n8n.online"
      FRONTEND_URL: "https://postiz.eric-n8n.online"
      NEXT_PUBLIC_BACKEND_URL: "https://postiz.eric-n8n.online/api"
      
      # 安全密钥 - 已生成的唯一JWT密钥
      JWT_SECRET: "Kj8#mN2$pQ9@vR7&xZ4!wE6*tY1^uI3%oP5+sA8-lF0~gH2"
 
      # 数据库配置
      DATABASE_URL: "postgresql://postiz-user:PostizDB#2024$Secure!Pass789@postiz-postgres:5432/postiz-db-local"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"
      
      # 自托管必需配置
      IS_GENERAL: "true"
      
      # 单用户注册模式 - 注册一个用户后禁用注册
      DISABLE_REGISTRATION: "true"
      
      # 文件存储配置
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      
    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - 5000:5000
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy
 
  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: "PostizDB#2024$Secure!Pass789"
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db-local
    volumes:
      - postgres-volume:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db-local
      interval: 10s
      timeout: 3s
      retries: 3
      
  postiz-redis:
    image: redis:7.2
    container_name: postiz-redis
    restart: always
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network
 
volumes:
  postgres-volume:
    external: false
 
  postiz-redis-data:
    external: false
 
  postiz-config:
    external: false
 
  postiz-uploads:
    external: false
 
networks:
  postiz-network:
    external: false
