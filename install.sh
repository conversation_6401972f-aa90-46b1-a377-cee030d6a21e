#!/bin/bash

# Postiz 快速安装脚本
# 适用于 DigitalOcean Ubuntu 22.04.5

echo "🚀 开始安装 Postiz..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then 
    echo "❌ 请以 root 用户运行此脚本"
    exit 1
fi

# 更新系统
echo "📦 更新系统包..."
apt update

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 安装 Docker Compose 插件（如果需要）
echo "🔧 检查 Docker Compose..."
if ! docker compose version &> /dev/null; then
    echo "📦 安装 Docker Compose 插件..."
    apt install docker-compose-plugin -y
fi

# 创建 Postiz 目录
echo "📁 创建 Postiz 目录..."
mkdir -p /opt/postiz
cd /opt/postiz

# 检查是否已存在 docker-compose.yml
if [ -f "docker-compose.yml" ]; then
    echo "⚠️  发现现有的 docker-compose.yml 文件"
    read -p "是否要备份并替换？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        mv docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)
        echo "✅ 已备份现有配置文件"
    else
        echo "❌ 安装取消"
        exit 1
    fi
fi

# 创建 docker-compose.yml 文件
echo "📝 创建 docker-compose.yml 配置文件..."
cat > docker-compose.yml << 'EOF'
services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz
    restart: always
    environment:
      # 域名配置
      MAIN_URL: "https://postiz.eric-n8n.online"
      FRONTEND_URL: "https://postiz.eric-n8n.online"
      NEXT_PUBLIC_BACKEND_URL: "https://postiz.eric-n8n.online/api"
      
      # 安全密钥
      JWT_SECRET: "Kj8#mN2$pQ9@vR7&xZ4!wE6*tY1^uI3%oP5+sA8-lF0~gH2"
 
      # 数据库配置
      DATABASE_URL: "postgresql://postiz-user:PostizDB#2024$Secure!Pass789@postiz-postgres:5432/postiz-db-local"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"
      
      # 自托管配置
      IS_GENERAL: "true"
      DISABLE_REGISTRATION: "true"
      
      # 文件存储
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      
      # Gmail SMTP 配置 (需要设置应用专用密码)
      EMAIL_PROVIDER: "nodemailer"
      EMAIL_FROM_NAME: "Postiz Notification"
      EMAIL_FROM_ADDRESS: "<EMAIL>"
      EMAIL_HOST: "smtp.gmail.com"
      EMAIL_PORT: "587"
      EMAIL_SECURE: "false"
      EMAIL_USER: "<EMAIL>"
      EMAIL_PASS: "YOUR_GMAIL_APP_PASSWORD_HERE"
      
    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - 5000:5000
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy
 
  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: "PostizDB#2024$Secure!Pass789"
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db-local
    volumes:
      - postgres-volume:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db-local
      interval: 10s
      timeout: 3s
      retries: 3
      
  postiz-redis:
    image: redis:7.2
    container_name: postiz-redis
    restart: always
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network
 
volumes:
  postgres-volume:
    external: false
  postiz-redis-data:
    external: false
  postiz-config:
    external: false
  postiz-uploads:
    external: false
 
networks:
  postiz-network:
    external: false
EOF

echo "✅ 配置文件创建完成"

# 启动服务
echo "🚀 启动 Postiz 服务..."
docker compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker compose ps

echo ""
echo "🎉 Postiz 安装完成！"
echo ""
echo "📋 安装信息："
echo "   访问地址: https://postiz.eric-n8n.online"
echo "   JWT密钥: Kj8#mN2$pQ9@vR7&xZ4!wE6*tY1^uI3%oP5+sA8-lF0~gH2"
echo "   数据库密码: PostizDB#2024$Secure!Pass789"
echo "   单用户模式: 已启用"
echo ""
echo "📝 后续步骤："
echo "   1. 访问 https://postiz.eric-n8n.online"
echo "   2. 注册第一个用户账户"
echo "   3. 配置社交媒体平台连接"
echo ""
echo "🔧 管理命令："
echo "   查看日志: docker compose logs -f postiz"
echo "   重启服务: docker compose restart"
echo "   停止服务: docker compose down"
echo ""
echo "📚 更多信息请查看: Postiz安装教程.md"
