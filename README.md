# Postiz 安装项目

## 📋 项目概述
在 DigitalOcean 上安装 Postiz 社交媒体管理平台

## ✅ 环境准备
- [x] DigitalOcean Droplet (Ubuntu)
- [x] Docker 已安装
- [x] 域名：postiz.eric-n8n.online (已解析)
- [x] HTTPS 证书已配置
- [x] 邮箱：<EMAIL>

## 🚀 快速安装

### 方法一：一键安装脚本
```bash
# 以 root 用户运行
chmod +x install.sh
./install.sh
```

### 方法二：手动安装
```bash
# 1. 创建目录
mkdir -p /opt/postiz && cd /opt/postiz

# 2. 复制 docker-compose.yml 文件到目录

# 3. 启动服务
docker compose up -d

# 4. 检查状态
docker compose ps
```

## 📁 文件说明
- `docker-compose.yml` - 主配置文件（已配置好域名和密钥）
- `install.sh` - 一键安装脚本
- `Postiz安装教程.md` - 详细安装教程

## 🔗 访问地址
- 网站：https://postiz.eric-n8n.online
- 首次访问需要注册管理员账户

## 📚 官方文档
- [Docker Compose 安装](https://docs.postiz.com/installation/docker-compose)
- [Docker 配置](https://docs.postiz.com/configuration/docker)
- [配置参考](https://docs.postiz.com/configuration/reference)
- [邮件配置](https://docs.postiz.com/configuration/emails)

## 🛠️ 管理命令
```bash
# 查看日志
docker compose logs -f postiz

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 更新服务
docker compose pull && docker compose up -d
```

## ⚠️ 重要提醒
1. 严格按照官方文档操作
2. 遇到问题先查阅文档
3. JWT_SECRET 和数据库密码已预设，请勿泄露
4. 单用户模式已启用，注册一个账户后自动禁用注册