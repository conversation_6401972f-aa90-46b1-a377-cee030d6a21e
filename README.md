# Postiz Docker Compose 安装

## 📋 目标
按照官方文档在 DigitalOcean 上安装 Postiz：
https://docs.postiz.com/installation/docker-compose

## ✅ 前置条件
- [x] DigitalOcean Droplet (Ubuntu 24.04, 2GB RAM, 2 vCPUs)
- [x] Docker 已安装
- [x] 域名：postiz.eric-n8n.online (已解析)
- [x] HTTPS 已配置

## 🚀 安装步骤 (严格按照官方文档)

### 1. 连接服务器
```bash
ssh root@your-server-ip
```

### 2. 创建目录
```bash
mkdir -p /opt/postiz
cd /opt/postiz
```

### 3. 创建 docker-compose.yml
将项目中的 `docker-compose.yml` 文件内容复制到服务器

### 4. 启动服务
```bash
# 官方文档命令
docker compose up

# 后台运行 (推荐)
docker compose up -d
```

### 5. 访问应用
打开浏览器访问：https://postiz.eric-n8n.online

## 📁 配置说明
- `docker-compose.yml` - 官方标准配置，已设置域名为 postiz.eric-n8n.online
- JWT_SECRET 已生成随机密钥
- 使用官方默认的数据库密码

## ⚠️ 重要提醒
1. **严格按照官方文档操作**
2. 当前是基础安装，后续功能配置：
   - 邮件通知 (可选)
   - 社交媒体平台连接
   - 其他高级配置

## 📚 官方文档
- [Docker Compose 安装](https://docs.postiz.com/installation/docker-compose)

## 🛠️ 基本管理命令
```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f postiz

# 停止服务
docker compose down

# 重启服务 (修改配置后)
docker compose down && docker compose up -d
```