# Postiz 官方安装教程 - 清除重装

## 🔄 清除之前的安装

### 1. 完全清理系统
```bash
# 停止所有容器
docker stop $(docker ps -aq) 2>/dev/null

# 删除所有容器
docker rm $(docker ps -aq) 2>/dev/null

# 删除所有卷
docker volume prune -f

# 清理系统
docker system prune -a -f

# 删除之前的目录
rm -rf /opt/postiz
```

## 📋 系统要求
- Ubuntu 24.04 (推荐) 或更高版本
- 至少 2GB RAM
- 至少 2 vCPU
- Docker 已安装
- IP地址：***************

## 🚀 一键安装脚本

**直接复制以下整个脚本到终端中执行：**

```bash
#!/bin/bash
echo "=== Postiz 官方安装脚本 ==="

# 1. 清理之前的安装
echo "🧹 清理之前的安装..."
docker stop $(docker ps -aq) 2>/dev/null
docker rm $(docker ps -aq) 2>/dev/null
docker volume prune -f
rm -rf /opt/postiz

# 2. 创建目录
echo "📁 创建安装目录..."
mkdir -p /opt/postiz
cd /opt/postiz

# 3. 创建官方配置文件
echo "⚙️ 创建配置文件..."
cat > docker-compose.yml << 'EOF'

services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz
    restart: always
    environment:
      # 使用域名访问 - eric-n8n.online
      MAIN_URL: "https://eric-n8n.online:5000"
      FRONTEND_URL: "https://eric-n8n.online:5000"
      NEXT_PUBLIC_BACKEND_URL: "https://eric-n8n.online:5000/api"

      # 简单的JWT密钥
      JWT_SECRET: "simple-jwt-secret-123456"

      # 数据库配置 - 简单密码
      DATABASE_URL: "*******************************************************/postiz-db-local"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"

      # 必需的自托管配置
      IS_GENERAL: "true"
      DISABLE_REGISTRATION: "false"

      # 文件存储配置
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"

      # 使用HTTPS的配置（域名访问）
      NOT_SECURED: "false"

    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - 5000:5000
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy
  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: simple123
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db-local
    volumes:
      - postgres-volume:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db-local
      interval: 10s
      timeout: 3s
      retries: 3

  postiz-redis:
    image: redis:7.2
    container_name: postiz-redis
    restart: always
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network

volumes:
  postgres-volume:
    external: false
  postiz-redis-data:
    external: false
  postiz-config:
    external: false
  postiz-uploads:
    external: false

networks:
  postiz-network:
    external: false
EOF

# 4. 启动服务
echo "🚀 启动 Postiz 服务..."
docker compose up -d

echo ""
echo "=== 安装完成 ==="
echo "✅ Postiz 正在启动中..."
echo "🌐 访问地址: https://eric-n8n.online:5000"
echo "⏱️  请等待 2-3 分钟让服务完全启动"
echo ""
echo "📊 查看状态: docker compose ps"
echo "📋 查看日志: docker compose logs -f"
echo "🔄 重启服务: docker compose restart"
echo "🛑 停止服务: docker compose down"
```

## 📝 手动安装步骤（如果一键脚本失败）

### 1. 创建目录
```bash
mkdir -p /opt/postiz && cd /opt/postiz
```

### 2. 创建配置文件
```bash
nano docker-compose.yml
```
然后复制上面脚本中的配置内容。

### 3. 启动服务
```bash
docker compose up -d
```

## 🔧 常用命令

```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 完全清理（包括数据）
docker compose down -v
```

## 🌐 访问应用

安装完成后，访问：**https://eric-n8n.online:5000**

## 📋 配置说明

- **用户名/密码**：首次访问时创建管理员账户
- **数据库密码**：simple123（简单密码）
- **JWT密钥**：simple-jwt-secret-123456
- **注册**：允许注册（DISABLE_REGISTRATION: "false"）
- **HTTPS**：已禁用（NOT_SECURED: "true"）

## 🔍 故障排除

### 如果服务无法启动：
```bash
# 查看详细日志
docker compose logs postiz

# 检查端口占用
netstat -tlnp | grep 5000

# 重新拉取镜像
docker compose pull && docker compose up -d
```

### 如果无法访问：
1. 检查防火墙设置
2. 确认端口5000已开放
3. 检查DigitalOcean防火墙规则


## 🎉 安装完成！

按照上面的一键脚本安装后，你就可以访问 **https://eric-n8n.online:5000** 开始使用Postiz了！

### 📚 更多资源
- [官方文档](https://docs.postiz.com/)
- [提供商配置](https://docs.postiz.com/providers)
- [GitHub仓库](https://github.com/gitroomhq/postiz-app)
