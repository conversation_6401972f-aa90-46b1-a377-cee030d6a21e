# Postiz 在 DigitalOcean 上的完整安装教程

## 前置条件检查 ✅
- [x] DigitalOcean Droplet (Ubuntu 22.04.5)
- [x] Docker 已安装
- [x] 域名 postiz.eric-n8n.online 已解析
- [x] HTTPS 已配置
- [x] 邮箱配置：<EMAIL>

## 第一步：连接到服务器

通过 DigitalOcean 控制台或 SSH 连接到你的服务器：
```bash
ssh root@128.199.246.131
```

## 第二步：创建 Postiz 目录

```bash
# 创建 postiz 目录
mkdir -p /opt/postiz
cd /opt/postiz
```

## 第三步：创建 docker-compose.yml 文件

```bash
# 创建配置文件
nano docker-compose.yml
```

将以下内容复制粘贴到文件中：

```yaml
services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz
    restart: always
    environment:
      # 域名配置
      MAIN_URL: "https://postiz.eric-n8n.online"
      FRONTEND_URL: "https://postiz.eric-n8n.online"
      NEXT_PUBLIC_BACKEND_URL: "https://postiz.eric-n8n.online/api"
      
      # 安全密钥
      JWT_SECRET: "Kj8#mN2$pQ9@vR7&xZ4!wE6*tY1^uI3%oP5+sA8-lF0~gH2"
 
      # 数据库配置
      DATABASE_URL: "postgresql://postiz-user:PostizDB%232024%24Secure%21Pass789@postiz-postgres:5432/postiz-db-local"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"
      
      # 自托管配置
      IS_GENERAL: "true"
      DISABLE_REGISTRATION: "true"
      
      # 文件存储
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      
      # Gmail SMTP 配置
      EMAIL_PROVIDER: "nodemailer"
      EMAIL_FROM_NAME: "Postiz Notification"
      EMAIL_FROM_ADDRESS: "<EMAIL>"
      EMAIL_HOST: "smtp.gmail.com"
      EMAIL_PORT: "587"
      EMAIL_SECURE: "false"
      EMAIL_USER: "<EMAIL>"
      EMAIL_PASS: "19910706.."
      
    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - 5000:5000
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy
 
  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: "PostizDB#2024$Secure!Pass789"
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db-local
    volumes:
      - postgres-volume:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db-local
      interval: 10s
      timeout: 3s
      retries: 3
      
  postiz-redis:
    image: redis:7.2
    container_name: postiz-redis
    restart: always
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network
 
volumes:
  postgres-volume:
    external: false
  postiz-redis-data:
    external: false
  postiz-config:
    external: false
  postiz-uploads:
    external: false
 
networks:
  postiz-network:
    external: false
```

保存文件：按 `Ctrl + X`，然后按 `Y`，最后按 `Enter`

## 第四步：安装 Docker Compose（如果需要）

检查 Docker Compose 是否可用：
```bash
docker compose version
```

如果提示命令不存在，安装 Docker Compose：
```bash
apt update
apt install docker-compose-plugin -y
```

## 第五步：启动 Postiz

```bash
# 启动所有服务
docker compose up -d
```

## 第六步：检查服务状态

```bash
# 查看运行中的容器
docker ps

# 查看服务日志
docker compose logs -f postiz
```

## 第七步：验证安装

1. **检查容器状态**：
```bash
docker compose ps
```
所有服务应该显示为 "Up" 状态。

2. **访问 Postiz**：
打开浏览器访问：https://postiz.eric-n8n.online

3. **首次注册**：
- 不要点击 "Continue with Google"（未配置）
- 使用邮箱注册新账户
- 由于启用了单用户模式，注册一个用户后将自动禁用注册

## 故障排除

### 如果容器启动失败：
```bash
# 查看详细日志
docker compose logs postiz
docker compose logs postiz-postgres
docker compose logs postiz-redis
```

### 如果端口被占用：
```bash
# 检查端口使用情况
netstat -tlnp | grep :5000
```

### 重启服务：
```bash
# 停止服务
docker compose down

# 重新启动
docker compose up -d
```

## 重要安全信息

1. **JWT_SECRET**: `Kj8#mN2$pQ9@vR7&xZ4!wE6*tY1^uI3%oP5+sA8-lF0~gH2`
2. **数据库密码**: `PostizDB#2024$Secure!Pass789`
3. **单用户模式**: 已启用，注册一个用户后自动禁用注册

## 后续配置

安装完成后，你需要在 Postiz 界面中手动配置：
- 社交媒体平台连接（Twitter/X, LinkedIn, Facebook 等）
- 其他高级设置

参考官方文档：https://docs.postiz.com/providers

## 维护命令

```bash
# 更新 Postiz
docker compose pull
docker compose up -d

# 备份数据
docker compose exec postiz-postgres pg_dump -U postiz-user postiz-db-local > backup.sql

# 查看资源使用
docker stats
```

安装完成！🎉
